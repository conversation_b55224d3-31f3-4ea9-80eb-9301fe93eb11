{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/InformazioniBase.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { CertificazioneCavoCreate, StrumentoCertificato } from '@/types/certificazioni'\n\ninterface InformazioniBaseProps {\n  formData: Partial<CertificazioneCavoCreate>\n  cavi: any[]\n  responsabili: any[]\n  strumenti: StrumentoCertificato[]\n  validationErrors: Record<string, string>\n  isCavoLocked: boolean\n  onInputChange: (field: string, value: any) => void\n}\n\nexport function InformazioniBase({\n  formData,\n  cavi,\n  responsabili,\n  strumenti,\n  validationErrors,\n  isCavoLocked,\n  onInputChange\n}: InformazioniBaseProps) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n      {/* Cavo */}\n      <div className=\"space-y-1\">\n        <Label htmlFor=\"id_cavo\" className=\"text-xs font-medium text-gray-700\">Cavo *</Label>\n        <Select\n          value={formData.id_cavo}\n          onValueChange={(value) => onInputChange('id_cavo', value)}\n          disabled={isCavoLocked}\n        >\n          <SelectTrigger className={`h-11 text-sm ${validationErrors.id_cavo ? 'border-red-500' : 'border-gray-300'}`}>\n            <SelectValue placeholder=\"Seleziona cavo...\" />\n          </SelectTrigger>\n          <SelectContent>\n            {cavi.map((cavo) => (\n              <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                <div className=\"flex flex-col py-1\">\n                  <span className=\"font-medium text-sm\">{cavo.id_cavo}</span>\n                  <span className=\"text-xs text-gray-500\">{cavo.tipologia} {cavo.sezione}</span>\n                </div>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n        {validationErrors.id_cavo && (\n          <p className=\"text-sm text-red-600\">{validationErrors.id_cavo}</p>\n        )}\n      </div>\n\n      {/* Operatore */}\n      <div className=\"space-y-1\">\n        <Label htmlFor=\"id_operatore\" className=\"text-xs font-medium text-gray-700\">Operatore</Label>\n        <Select\n          value={formData.id_operatore?.toString() || ''}\n          onValueChange={(value) => onInputChange('id_operatore', parseInt(value))}\n        >\n          <SelectTrigger className=\"h-11 text-sm border-gray-300\">\n            <SelectValue placeholder=\"Seleziona operatore...\" />\n          </SelectTrigger>\n          <SelectContent>\n            {responsabili.map((resp) => (\n              <SelectItem key={resp.id_responsabile} value={resp.id_responsabile.toString()}>\n                <span className=\"text-sm\">{resp.nome_responsabile}</span>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Strumento */}\n      <div className=\"space-y-1\">\n        <Label htmlFor=\"id_strumento\" className=\"text-xs font-medium text-gray-700\">Strumento di Misura</Label>\n        <Select\n          value={formData.id_strumento?.toString() || ''}\n          onValueChange={(value) => {\n            const strumento = strumenti.find(s => s.id_strumento === parseInt(value))\n            onInputChange('id_strumento', parseInt(value))\n            if (strumento) {\n              onInputChange('strumento_utilizzato', `${strumento.marca} ${strumento.modello}`)\n            }\n          }}\n        >\n          <SelectTrigger className=\"h-11 text-sm border-gray-300\">\n            <SelectValue placeholder=\"Seleziona strumento...\" />\n          </SelectTrigger>\n          <SelectContent>\n            {strumenti.map((strumento) => (\n              <SelectItem key={strumento.id_strumento} value={strumento.id_strumento.toString()}>\n                <div className=\"flex flex-col py-1\">\n                  <span className=\"font-medium text-sm\">{strumento.nome}</span>\n                  <span className=\"text-xs text-gray-500\">{strumento.marca} {strumento.modello}</span>\n                </div>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Tipo Certificato */}\n      <div className=\"space-y-1\">\n        <Label htmlFor=\"tipo_certificato\" className=\"text-xs font-medium text-gray-700\">Tipo Certificato</Label>\n        <Select\n          value={formData.tipo_certificato || 'SINGOLO'}\n          onValueChange={(value) => onInputChange('tipo_certificato', value)}\n        >\n          <SelectTrigger className=\"h-11 text-sm border-gray-300\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"SINGOLO\">\n              <span className=\"text-sm\">🔍 Singolo</span>\n            </SelectItem>\n            <SelectItem value=\"GRUPPO\">\n              <span className=\"text-sm\">📊 Gruppo</span>\n            </SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAuBO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;kCAAoC;;;;;;kCACvE,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,OAAO;wBACvB,eAAe,CAAC,QAAU,cAAc,WAAW;wBACnD,UAAU;;0CAEV,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAW,CAAC,aAAa,EAAE,iBAAiB,OAAO,GAAG,mBAAmB,mBAAmB;0CACzG,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;0CACX,KAAK,GAAG,CAAC,CAAC,qBACT,6LAAC,qIAAA,CAAA,aAAU;wCAAoB,OAAO,KAAK,OAAO;kDAChD,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAuB,KAAK,OAAO;;;;;;8DACnD,6LAAC;oDAAK,WAAU;;wDAAyB,KAAK,SAAS;wDAAC;wDAAE,KAAK,OAAO;;;;;;;;;;;;;uCAHzD,KAAK,OAAO;;;;;;;;;;;;;;;;oBASlC,iBAAiB,OAAO,kBACvB,6LAAC;wBAAE,WAAU;kCAAwB,iBAAiB,OAAO;;;;;;;;;;;;0BAKjE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAe,WAAU;kCAAoC;;;;;;kCAC5E,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,YAAY,EAAE,cAAc;wBAC5C,eAAe,CAAC,QAAU,cAAc,gBAAgB,SAAS;;0CAEjE,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;0CACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;wCAA4B,OAAO,KAAK,eAAe,CAAC,QAAQ;kDACzE,cAAA,6LAAC;4CAAK,WAAU;sDAAW,KAAK,iBAAiB;;;;;;uCADlC,KAAK,eAAe;;;;;;;;;;;;;;;;;;;;;;0BAS7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAe,WAAU;kCAAoC;;;;;;kCAC5E,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,YAAY,EAAE,cAAc;wBAC5C,eAAe,CAAC;4BACd,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,SAAS;4BAClE,cAAc,gBAAgB,SAAS;4BACvC,IAAI,WAAW;gCACb,cAAc,wBAAwB,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO,EAAE;4BACjF;wBACF;;0CAEA,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,0BACd,6LAAC,qIAAA,CAAA,aAAU;wCAA8B,OAAO,UAAU,YAAY,CAAC,QAAQ;kDAC7E,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAuB,UAAU,IAAI;;;;;;8DACrD,6LAAC;oDAAK,WAAU;;wDAAyB,UAAU,KAAK;wDAAC;wDAAE,UAAU,OAAO;;;;;;;;;;;;;uCAH/D,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;0BAY/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAmB,WAAU;kCAAoC;;;;;;kCAChF,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,gBAAgB,IAAI;wBACpC,eAAe,CAAC,QAAU,cAAc,oBAAoB;;0CAE5D,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;KA7GgB", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/CondizioniAmbientali.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\nimport { Cloud, Loader2, Settings, X } from 'lucide-react'\nimport { CertificazioneCavoCreate } from '@/types/certificazioni'\n\ninterface WeatherData {\n  temperature: number\n  humidity: number\n  city?: string\n  isDemo: boolean\n  source: string\n}\n\ninterface CondizioniAmbientaliProps {\n  formData: Partial<CertificazioneCavoCreate>\n  weatherData: WeatherData | null\n  isLoadingWeather: boolean\n  isWeatherOverride: boolean\n  onInputChange: (field: string, value: any) => void\n  onToggleWeatherOverride: () => void\n}\n\nexport function CondizioniAmbientali({\n  formData,\n  weatherData,\n  isLoadingWeather,\n  isWeatherOverride,\n  onInputChange,\n  onToggleWeatherOverride\n}: CondizioniAmbientaliProps) {\n  if (!weatherData) return null\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      {/* Dati Meteorologici */}\n      <div className={`p-4 rounded-lg border-2 col-span-full ${\n        weatherData.isDemo\n          ? 'bg-amber-50 border-amber-200'\n          : 'bg-emerald-50 border-emerald-200'\n      }`}>\n        <div className=\"flex items-start gap-4\">\n          {isLoadingWeather ? (\n            <Loader2 className=\"h-5 w-5 animate-spin text-blue-600 mt-1\" />\n          ) : (\n            <div className=\"text-2xl\">\n              {weatherData.isDemo ? '🔧' : '🌤️'}\n            </div>\n          )}\n\n          <div className=\"flex-1\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <div>\n                <div className=\"text-lg font-semibold text-gray-900\">\n                  {weatherData.temperature}°C • {weatherData.humidity}% UR\n                </div>\n                {weatherData.city && (\n                  <div className=\"text-sm text-gray-600\">{weatherData.city}</div>\n                )}\n              </div>\n\n              <Button\n                type=\"button\"\n                variant={isWeatherOverride ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={onToggleWeatherOverride}\n                className=\"h-8\"\n              >\n                {isWeatherOverride ? (\n                  <>\n                    <X className=\"h-3 w-3 mr-1\" />\n                    Automatico\n                  </>\n                ) : (\n                  <>\n                    <Settings className=\"h-3 w-3 mr-1\" />\n                    Manuale\n                    </>\n                  )}\n                </Button>\n              </div>\n\n            <div className=\"text-xs text-gray-500\">\n              📡 {weatherData.source}\n              {weatherData.isDemo && ' • Dati dimostrativi'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Override Manuale */}\n      {isWeatherOverride && (\n        <div className=\"col-span-full p-4 bg-blue-50 border-2 border-blue-200 rounded-lg\">\n          <div className=\"flex items-center gap-2 mb-4\">\n            <Settings className=\"h-4 w-4 text-blue-600\" />\n            <span className=\"text-sm font-medium text-blue-800\">\n              Inserimento Manuale Parametri\n            </span>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"temperatura_prova\" className=\"text-xs font-medium text-gray-700\">\n                Temperatura (°C)\n              </Label>\n              <Input\n                id=\"temperatura_prova\"\n                type=\"number\"\n                step=\"0.1\"\n                value={formData.temperatura_prova || ''}\n                onChange={(e) => onInputChange('temperatura_prova', parseFloat(e.target.value))}\n                placeholder=\"20.0\"\n                className=\"h-11 text-sm\"\n              />\n              <p className=\"text-xs text-gray-500\">Range tipico: 15-30°C</p>\n            </div>\n\n            <div className=\"space-y-1\">\n              <Label htmlFor=\"umidita_prova\" className=\"text-xs font-medium text-gray-700\">\n                Umidità Relativa (%)\n              </Label>\n              <Input\n                id=\"umidita_prova\"\n                type=\"number\"\n                min=\"0\"\n                max=\"100\"\n                value={formData.umidita_prova || ''}\n                onChange={(e) => onInputChange('umidita_prova', parseFloat(e.target.value))}\n                placeholder=\"50\"\n                className=\"h-11 text-sm\"\n              />\n              <p className=\"text-xs text-gray-500\">Range tipico: 30-70%</p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;AA0BO,SAAS,qBAAqB,EACnC,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,iBAAiB,EACjB,aAAa,EACb,uBAAuB,EACG;IAC1B,IAAI,CAAC,aAAa,OAAO;IAEzB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAC,sCAAsC,EACrD,YAAY,MAAM,GACd,iCACA,oCACJ;0BACA,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,iCACC,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,6LAAC;4BAAI,WAAU;sCACZ,YAAY,MAAM,GAAG,OAAO;;;;;;sCAIjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;wDACZ,YAAY,WAAW;wDAAC;wDAAM,YAAY,QAAQ;wDAAC;;;;;;;gDAErD,YAAY,IAAI,kBACf,6LAAC;oDAAI,WAAU;8DAAyB,YAAY,IAAI;;;;;;;;;;;;sDAI5D,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,oBAAoB,YAAY;4CACzC,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,kCACC;;kEACE,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;6EAIhC;;kEACE,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;8CAO7C,6LAAC;oCAAI,WAAU;;wCAAwB;wCACjC,YAAY,MAAM;wCACrB,YAAY,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;YAO9B,mCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;kCAKtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAoB,WAAU;kDAAoC;;;;;;kDAGjF,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,iBAAiB,IAAI;wCACrC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC7E,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAgB,WAAU;kDAAoC;;;;;;kDAG7E,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO,SAAS,aAAa,IAAI;wCACjC,UAAU,CAAC,IAAM,cAAc,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;wCACzE,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;KAnHgB", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/MisurazioniTest.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { Label } from '@/components/ui/label'\r\nimport { Input } from '@/components/ui/input'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select'\r\nimport { CertificazioneCavoCreate } from '@/types/certificazioni'\r\n\r\ninterface MisurazioniTestProps {\r\n  formData: Partial<CertificazioneCavoCreate>\r\n  validationErrors: Record<string, string>\r\n  onInputChange: (field: string, value: any) => void\r\n}\r\n\r\nexport function MisurazioniTest({\r\n  formData,\r\n  validationErrors,\r\n  onInputChange\r\n}: MisurazioniTestProps) {\r\n  return (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n      {/* Isolamento */}\r\n      <div className=\"space-y-1\">\r\n        <Label htmlFor=\"valore_isolamento\" className=\"text-xs font-medium text-gray-700\">\r\n          Isolamento (MΩ) *\r\n        </Label>\r\n        <Input\r\n          id=\"valore_isolamento\"\r\n          type=\"number\"\r\n          step=\"0.01\"\r\n          value={formData.valore_isolamento || ''}\r\n          onChange={(e) => onInputChange('valore_isolamento', parseFloat(e.target.value))}\r\n          placeholder=\"≥ 1000\"\r\n          className={`h-9 text-sm ${validationErrors.valore_isolamento ? 'border-red-500' : 'border-gray-300'}`}\r\n        />\r\n        {validationErrors.valore_isolamento && (\r\n          <p className=\"text-sm text-red-600\">{validationErrors.valore_isolamento}</p>\r\n        )}\r\n        <p className=\"text-xs text-gray-500\">Valore minimo: 1000 MΩ</p>\r\n      </div>\r\n\r\n      {/* Test Continuità */}\r\n      <div className=\"space-y-1\">\r\n        <Label htmlFor=\"valore_continuita\" className=\"text-xs font-medium text-gray-700\">\r\n          Test Continuità *\r\n        </Label>\r\n        <Select\r\n          value={formData.valore_continuita}\r\n          onValueChange={(value) => onInputChange('valore_continuita', value)}\r\n        >\r\n          <SelectTrigger className={`h-9 text-sm ${validationErrors.valore_continuita ? 'border-red-500' : 'border-gray-300'}`}>\r\n            <SelectValue placeholder=\"Seleziona esito...\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"CONFORME\">\r\n              <span className=\"text-sm\">✅ CONFORME</span>\r\n            </SelectItem>\r\n            <SelectItem value=\"NON_CONFORME\">\r\n              <span className=\"text-sm\">❌ NON CONFORME</span>\r\n            </SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n        {validationErrors.valore_continuita && (\r\n          <p className=\"text-sm text-red-600\">{validationErrors.valore_continuita}</p>\r\n        )}\r\n      </div>\r\n\r\n      {/* Resistenza */}\r\n      <div className=\"space-y-1\">\r\n        <Label htmlFor=\"valore_resistenza\" className=\"text-xs font-medium text-gray-700\">\r\n          Resistenza (Ω) *\r\n        </Label>\r\n        <Input\r\n          id=\"valore_resistenza\"\r\n          type=\"number\"\r\n          step=\"0.01\"\r\n          value={formData.valore_resistenza || ''}\r\n          onChange={(e) => onInputChange('valore_resistenza', parseFloat(e.target.value))}\r\n          placeholder=\"< 1.0\"\r\n          className={`h-9 text-sm ${validationErrors.valore_resistenza ? 'border-red-500' : 'border-gray-300'}`}\r\n        />\r\n        {validationErrors.valore_resistenza && (\r\n          <p className=\"text-sm text-red-600\">{validationErrors.valore_resistenza}</p>\r\n        )}\r\n        <p className=\"text-xs text-gray-500\">Valore massimo: 1.0 Ω</p>\r\n      </div>\r\n\r\n      {/* Tensione Prova Isolamento */}\r\n      <div className=\"space-y-1\">\r\n        <Label htmlFor=\"tensione_prova_isolamento\" className=\"text-xs font-medium text-gray-700\">\r\n          Tensione di Prova (V)\r\n        </Label>\r\n        <Input\r\n          id=\"tensione_prova_isolamento\"\r\n          type=\"number\"\r\n          value={formData.tensione_prova_isolamento || 500}\r\n          onChange={(e) => onInputChange('tensione_prova_isolamento', parseInt(e.target.value))}\r\n          className=\"h-9 text-sm border-gray-300\"\r\n        />\r\n        <p className=\"text-xs text-gray-500\">Standard: 500V DC</p>\r\n      </div>\r\n\r\n      {/* Durata Prova */}\r\n      <div className=\"space-y-1\">\r\n        <Label htmlFor=\"durata_prova\" className=\"text-xs font-medium text-gray-700\">\r\n          Durata Prova (min)\r\n        </Label>\r\n        <Input\r\n          id=\"durata_prova\"\r\n          type=\"number\"\r\n          value={formData.durata_prova || 1}\r\n          onChange={(e) => onInputChange('durata_prova', parseInt(e.target.value))}\r\n          placeholder=\"1\"\r\n          className=\"h-9 text-sm border-gray-300\"\r\n        />\r\n        <p className=\"text-xs text-gray-500\">Standard: 1 minuto</p>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAoBO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,gBAAgB,EAChB,aAAa,EACQ;IACrB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAoB,WAAU;kCAAoC;;;;;;kCAGjF,6LAAC,oIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,MAAK;wBACL,OAAO,SAAS,iBAAiB,IAAI;wBACrC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wBAC7E,aAAY;wBACZ,WAAW,CAAC,YAAY,EAAE,iBAAiB,iBAAiB,GAAG,mBAAmB,mBAAmB;;;;;;oBAEtG,iBAAiB,iBAAiB,kBACjC,6LAAC;wBAAE,WAAU;kCAAwB,iBAAiB,iBAAiB;;;;;;kCAEzE,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAoB,WAAU;kCAAoC;;;;;;kCAGjF,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,iBAAiB;wBACjC,eAAe,CAAC,QAAU,cAAc,qBAAqB;;0CAE7D,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAW,CAAC,YAAY,EAAE,iBAAiB,iBAAiB,GAAG,mBAAmB,mBAAmB;0CAClH,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;oBAI/B,iBAAiB,iBAAiB,kBACjC,6LAAC;wBAAE,WAAU;kCAAwB,iBAAiB,iBAAiB;;;;;;;;;;;;0BAK3E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAoB,WAAU;kCAAoC;;;;;;kCAGjF,6LAAC,oIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,MAAK;wBACL,OAAO,SAAS,iBAAiB,IAAI;wBACrC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wBAC7E,aAAY;wBACZ,WAAW,CAAC,YAAY,EAAE,iBAAiB,iBAAiB,GAAG,mBAAmB,mBAAmB;;;;;;oBAEtG,iBAAiB,iBAAiB,kBACjC,6LAAC;wBAAE,WAAU;kCAAwB,iBAAiB,iBAAiB;;;;;;kCAEzE,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA4B,WAAU;kCAAoC;;;;;;kCAGzF,6LAAC,oIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,yBAAyB,IAAI;wBAC7C,UAAU,CAAC,IAAM,cAAc,6BAA6B,SAAS,EAAE,MAAM,CAAC,KAAK;wBACnF,WAAU;;;;;;kCAEZ,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAe,WAAU;kCAAoC;;;;;;kCAG5E,6LAAC,oIAAA,CAAA,QAAK;wBACJ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,YAAY,IAAI;wBAChC,UAAU,CAAC,IAAM,cAAc,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK;wBACtE,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAI7C;KAzGgB", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/sections/NoteEStato.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { CertificazioneCavoCreate } from '@/types/certificazioni'\n\ninterface NoteEStatoProps {\n  formData: Partial<CertificazioneCavoCreate>\n  onInputChange: (field: string, value: any) => void\n}\n\nexport function NoteEStato({\n  formData,\n  onInputChange\n}: NoteEStatoProps) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n      {/* Note */}\n      <div className=\"space-y-1 col-span-full\">\n        <Label htmlFor=\"note\" className=\"text-xs font-medium text-gray-700\">\n          Note Aggiuntive\n        </Label>\n        <Textarea\n          id=\"note\"\n          value={formData.note || ''}\n          onChange={(e) => onInputChange('note', e.target.value)}\n          placeholder=\"Inserisci eventuali note, osservazioni o anomalie riscontrate durante la certificazione...\"\n          rows={4}\n          className=\"resize-none text-sm border-gray-300\"\n        />\n        <p className=\"text-xs text-gray-500\">\n          Descrivi eventuali condizioni particolari o osservazioni rilevanti\n        </p>\n      </div>\n\n      {/* Stato Certificazione */}\n      <div className=\"space-y-1\">\n        <Label htmlFor=\"stato_certificato\" className=\"text-xs font-medium text-gray-700\">\n          Stato Certificazione\n        </Label>\n        <Select\n          value={formData.stato_certificato || 'BOZZA'}\n          onValueChange={(value) => onInputChange('stato_certificato', value)}\n        >\n          <SelectTrigger className=\"h-11 text-sm border-gray-300\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"BOZZA\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 rounded-full bg-gray-400\"></div>\n                <span className=\"text-sm\">Bozza</span>\n                </div>\n            </SelectItem>\n            <SelectItem value=\"CONFORME\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                <span className=\"text-sm\">✅ Conforme</span>\n              </div>\n            </SelectItem>\n            <SelectItem value=\"NON_CONFORME\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n                <span className=\"text-sm\">❌ Non Conforme</span>\n              </div>\n            </SelectItem>\n            <SelectItem value=\"CONFORME_CON_RISERVA\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n                <span className=\"text-sm\">⚠️ Conforme con Riserva</span>\n              </div>\n            </SelectItem>\n          </SelectContent>\n        </Select>\n        <p className=\"text-xs text-gray-500\">\n          Lo stato verrà determinato automaticamente in base ai valori misurati\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAmBO,SAAS,WAAW,EACzB,QAAQ,EACR,aAAa,EACG;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAO,WAAU;kCAAoC;;;;;;kCAGpE,6LAAC,uIAAA,CAAA,WAAQ;wBACP,IAAG;wBACH,OAAO,SAAS,IAAI,IAAI;wBACxB,UAAU,CAAC,IAAM,cAAc,QAAQ,EAAE,MAAM,CAAC,KAAK;wBACrD,aAAY;wBACZ,MAAM;wBACN,WAAU;;;;;;kCAEZ,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAoB,WAAU;kCAAoC;;;;;;kCAGjF,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,iBAAiB,IAAI;wBACrC,eAAe,CAAC,QAAU,cAAc,qBAAqB;;0CAE7D,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,6LAAC,qIAAA,CAAA,gBAAa;;kDACZ,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;kDAG9B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;kDAG9B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;kDAG9B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAChB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKlC,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAM7C;KArEgB", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/certificazioni/CertificazioneForm.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { FileText, Save, Loader2, AlertCircle, X } from 'lucide-react'\nimport { CertificazioneCavo, StrumentoCertificato } from '@/types/certificazioni'\nimport { useCertificazioneForm } from '@/hooks/useCertificazioneForm'\n\n// Componenti sezioni\nimport { InformazioniBase } from './sections/InformazioniBase'\nimport { CondizioniAmbientali } from './sections/CondizioniAmbientali'\nimport { MisurazioniTest } from './sections/MisurazioniTest'\nimport { NoteEStato } from './sections/NoteEStato'\n\ninterface CertificazioneFormProps {\n  cantiereId: number\n  certificazione?: CertificazioneCavo | null\n  strumenti: StrumentoCertificato[]\n  preselectedCavoId?: string\n  onSuccess: (certificazione: CertificazioneCavo) => void\n  onCancel: () => void\n}\n\n/**\n * Form di certificazione CEI 64-8 completamente ridisegnato\n * Layout moderno a singola colonna con sezioni organizzate\n */\nexport default function CertificazioneForm({\n  cantiereId,\n  certificazione,\n  strumenti,\n  preselectedCavoId,\n  onSuccess,\n  onCancel\n}: CertificazioneFormProps) {\n  const {\n    // Dati\n    formData,\n    cavi,\n    responsabili,\n    weatherData,\n\n    // Stati\n    isLoading,\n    isSaving,\n    isLoadingWeather,\n    error,\n    validationErrors,\n    isWeatherOverride,\n    isEdit,\n    isCavoLocked,\n\n    // Funzioni\n    handleInputChange,\n    handleSubmit,\n    setIsWeatherOverride,\n    onCancel: handleCancel\n  } = useCertificazioneForm({\n    cantiereId,\n    certificazione,\n    strumenti,\n    preselectedCavoId,\n    onSuccess,\n    onCancel\n  })\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-96\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n          <p className=\"text-gray-600\">Caricamento dati certificazione...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Render del componente principale\n  return (\n    <div className=\"h-full w-full bg-gray-50\">\n      {/* Header Compatto */}\n      <div className=\"bg-white border-b border-gray-200 sticky top-0 z-10\">\n        <div className=\"max-w-4xl mx-auto px-4 py-2\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"p-1.5 bg-blue-100 rounded-lg\">\n                <FileText className=\"h-4 w-4 text-blue-600\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-semibold text-gray-900\">\n                  {isEdit ? 'Modifica Certificazione' : 'Nuova Certificazione CEI 64-8'}\n                </h1>\n                {preselectedCavoId && (\n                  <p className=\"text-xs text-gray-500\">Cavo: {preselectedCavoId}</p>\n                )}\n              </div>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onCancel}\n              className=\"text-gray-400 hover:text-gray-600 h-8 w-8 p-0\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Alert Errori */}\n      {error && (\n        <div className=\"bg-red-50 border-b border-red-200\">\n          <div className=\"max-w-4xl mx-auto px-4 py-2\">\n            <Alert variant=\"destructive\" className=\"border-red-200 bg-red-50 py-2\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription className=\"text-sm\">{error}</AlertDescription>\n            </Alert>\n          </div>\n        </div>\n      )}\n\n      {/* Contenuto Principale */}\n      <div className=\"max-w-4xl mx-auto px-4 py-4\">\n        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className=\"space-y-4\">\n\n          {/* Sezione 1: Informazioni Base */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-4 py-2 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-base font-semibold text-gray-900\">📋 Informazioni Base</h2>\n              <p className=\"text-xs text-gray-600\">Dati principali del cavo e operatore</p>\n            </div>\n            <div className=\"p-4\">\n              <InformazioniBase\n                formData={formData}\n                cavi={cavi}\n                responsabili={responsabili}\n                strumenti={strumenti}\n                validationErrors={validationErrors}\n                isCavoLocked={isCavoLocked}\n                onInputChange={handleInputChange}\n              />\n            </div>\n          </div>\n\n          {/* Sezione 2: Condizioni Ambientali */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-4 py-2 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-base font-semibold text-gray-900\">🌤️ Condizioni Ambientali</h2>\n              <p className=\"text-xs text-gray-600\">Temperatura e umidità durante la certificazione</p>\n            </div>\n            <div className=\"p-4\">\n              <CondizioniAmbientali\n                formData={formData}\n                weatherData={weatherData}\n                isLoadingWeather={isLoadingWeather}\n                isWeatherOverride={isWeatherOverride}\n                onInputChange={handleInputChange}\n                onToggleWeatherOverride={() => setIsWeatherOverride(!isWeatherOverride)}\n              />\n            </div>\n          </div>\n\n          {/* Sezione 3: Misurazioni e Test */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-4 py-2 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-base font-semibold text-gray-900\">⚡ Misurazioni e Test CEI 64-8</h2>\n              <p className=\"text-xs text-gray-600\">Valori di isolamento, continuità e parametri di prova</p>\n            </div>\n            <div className=\"p-4\">\n              <MisurazioniTest\n                formData={formData}\n                validationErrors={validationErrors}\n                onInputChange={handleInputChange}\n              />\n            </div>\n          </div>\n\n          {/* Sezione 4: Note e Stato */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n            <div className=\"px-4 py-2 border-b border-gray-100 bg-gray-50\">\n              <h2 className=\"text-base font-semibold text-gray-900\">📝 Note e Stato Certificazione</h2>\n              <p className=\"text-xs text-gray-600\">Osservazioni aggiuntive e stato finale</p>\n            </div>\n            <div className=\"p-4\">\n              <NoteEStato\n                formData={formData}\n                onInputChange={handleInputChange}\n              />\n            </div>\n          </div>\n\n          {/* Footer Azioni */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-3\">\n            <div className=\"flex justify-end gap-3\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={onCancel}\n                disabled={isSaving}\n                className=\"px-4 h-9\"\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isSaving}\n                className=\"px-4 h-9 bg-blue-600 hover:bg-blue-700\"\n              >\n                {isSaving ? (\n                  <>\n                    <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                    Salvando...\n                  </>\n                ) : (\n                  <>\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    {isEdit ? 'Aggiorna Certificazione' : 'Salva Certificazione'}\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;;;AAZA;;;;;;;;;AA2Be,SAAS,mBAAmB,EACzC,UAAU,EACV,cAAc,EACd,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACgB;;IACxB,MAAM,EACJ,OAAO;IACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,WAAW,EAEX,QAAQ;IACR,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,KAAK,EACL,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,EACN,YAAY,EAEZ,WAAW;IACX,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACpB,UAAU,YAAY,EACvB,GAAG,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mCAAmC;IACnC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,SAAS,4BAA4B;;;;;;4CAEvC,mCACC,6LAAC;gDAAE,WAAU;;oDAAwB;oDAAO;;;;;;;;;;;;;;;;;;;0CAIlD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOpB,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAc,WAAU;;0CACrC,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC,oIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAAW;;;;;;;;;;;;;;;;;;;;;;0BAO/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU,CAAC;wBAAQ,EAAE,cAAc;wBAAI;oBAAgB;oBAAG,WAAU;;sCAGxE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uKAAA,CAAA,mBAAgB;wCACf,UAAU;wCACV,MAAM;wCACN,cAAc;wCACd,WAAW;wCACX,kBAAkB;wCAClB,cAAc;wCACd,eAAe;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2KAAA,CAAA,uBAAoB;wCACnB,UAAU;wCACV,aAAa;wCACb,kBAAkB;wCAClB,mBAAmB;wCACnB,eAAe;wCACf,yBAAyB,IAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;sCAM3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,sKAAA,CAAA,kBAAe;wCACd,UAAU;wCACV,kBAAkB;wCAClB,eAAe;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iKAAA,CAAA,aAAU;wCACT,UAAU;wCACV,eAAe;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,yBACC;;8DACE,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,SAAS,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D;GAxMwB;;QA8BlB,wIAAA,CAAA,wBAAqB;;;KA9BH", "debugId": null}}]}