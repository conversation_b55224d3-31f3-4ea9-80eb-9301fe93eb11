{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "16afa75b0a04f5e0d8bb7d42eeca2dbb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "54d730b1fb77d9fa3576952cfd8fbfbba76b9a4f5e79c0c3fe85d402268d3096", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f5c3a5f0080d5e7350b83d71ee53dc94886d2917d4f810d3e669f64f1d85f4b5"}}}, "sortedMiddleware": ["/"], "functions": {}}